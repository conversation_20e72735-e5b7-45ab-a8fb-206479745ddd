CREATE TABLE 
    ListingMediaProcessingLog 
    ( 
        Id                    int NOT NULL IDENTITY, 
	ListingPictureId      int NOT NULL, 
        ListingId             int NOT NULL, 
        ListingNumber         varchar(50) NOT NULL, 
        SourceImageUrl        varchar(500) NOT NULL, 
        Status                varchar(20) NOT NULL, 
        StatusCode            int, 
        StatusMessage         varchar(200), 
        TaskId                varchar(100), 
        ImageUrl              varchar(500), 
        CreatedOn             datetime NOT NULL DEFAULT GETDATE(), 
        UpdatedOn             datetime NOT NULL DEFAULT GETDATE(), 
        CONSTRAINT PK_ListingMediaProcessingLog PRIMARY KEY (Id), 
        CONSTRAINT FK_ListingMediaProcessingLog_Listing FOREIGN KEY (ListingId) REFERENCES 
        Listings ON DELETE CASCADE 
    )