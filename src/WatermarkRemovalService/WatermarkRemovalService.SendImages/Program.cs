using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using Dapper;
using Refit;
using System.Net.Http.Headers;

namespace WatermarkRemovalService.SendImages;

public class Program
{
    public static async Task Main(string[] args)
    {
        // Build configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
            .Build();

        var connectionString = configuration.GetConnectionString("DataEntryContext")
            ?? throw new InvalidOperationException("Connection string 'DataEntryContext' not found.");
        var bearerToken = configuration["ApiSettings:BearerToken"]
            ?? throw new InvalidOperationException("Bearer token not found in configuration.");
        var apiBaseUrl = configuration["ApiSettings:BaseUrl"]
            ?? throw new InvalidOperationException("API base URL not found in configuration.");

        Console.WriteLine("Starting image processing task...");

        try
        {
            // Setup HTTP client with Bearer token
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", bearerToken);

            // Create Refit client
            var apiClient = RestService.For<IImageProcessingApi>(httpClient, new RefitSettings
            {
                ContentSerializer = new SystemTextJsonContentSerializer()
            });

            // Process images
            await ProcessImagesAsync(connectionString, apiClient);

            Console.WriteLine("Image processing task completed successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            Environment.Exit(1);
        }
    }

    private static async Task ProcessImagesAsync(string connectionString, IImageProcessingApi apiClient)
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        // Get all pending images
        var images = await connection.QueryAsync<ImageRecord>(@"
            SELECT Id, ImagePath, Status, CreatedAt
            FROM Images
            WHERE Status = 'Pending'
            ORDER BY CreatedAt ASC");

        Console.WriteLine($"Found {images.Count()} pending images to process.");

        int processed = 0;
        int successful = 0;
        int failed = 0;

        foreach (var image in images)
        {
            try
            {
                Console.WriteLine($"Processing image {image.Id}: {image.ImagePath}");

                // Update status to Processing
                await connection.ExecuteAsync(@"
                    UPDATE Images
                    SET Status = 'Processing', ProcessedAt = @ProcessedAt
                    WHERE Id = @Id",
                    new { Id = image.Id, ProcessedAt = DateTime.UtcNow });

                // Make API call
                var request = new ProcessImageRequest
                {
                    ImageId = image.Id,
                    ImagePath = image.ImagePath,
                    ProcessingType = "WatermarkRemoval"
                };

                var response = await apiClient.ProcessImageAsync(request);

                if (response.Success)
                {
                    // Update status to Completed
                    await connection.ExecuteAsync(@"
                        UPDATE Images
                        SET Status = 'Completed', ProcessedAt = @ProcessedAt
                        WHERE Id = @Id",
                        new { Id = image.Id, ProcessedAt = DateTime.UtcNow });

                    successful++;
                    Console.WriteLine($"✓ Successfully processed image {image.Id}");
                }
                else
                {
                    // Update status to Failed
                    await connection.ExecuteAsync(@"
                        UPDATE Images
                        SET Status = 'Failed', ProcessedAt = @ProcessedAt
                        WHERE Id = @Id",
                        new { Id = image.Id, ProcessedAt = DateTime.UtcNow });

                    failed++;
                    Console.WriteLine($"✗ Failed to process image {image.Id}: {response.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                // Update status to Failed
                await connection.ExecuteAsync(@"
                    UPDATE Images
                    SET Status = 'Failed', ProcessedAt = @ProcessedAt
                    WHERE Id = @Id",
                    new { Id = image.Id, ProcessedAt = DateTime.UtcNow });

                failed++;
                Console.WriteLine($"✗ Error processing image {image.Id}: {ex.Message}");
            }

            processed++;

            if (processed % 100 == 0)
            {
                Console.WriteLine($"Progress: {processed}/{images.Count()} processed");
            }
        }

        Console.WriteLine($"\nProcessing completed:");
        Console.WriteLine($"Total processed: {processed}");
        Console.WriteLine($"Successful: {successful}");
        Console.WriteLine($"Failed: {failed}");
    }
}
