using Dapper;
using Microsoft.Extensions.Logging;
using System.Data;

namespace WatermarkRemovalService.SendImages;

public class DataService : IDataService
{
    private readonly IDbConnection _connection;
    private readonly ILogger<DataService> _logger;

    public DataService(IDbConnection connection, ILogger<DataService> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    public async Task<IEnumerable<ImageRecord>> GetPendingImagesAsync()
    {
        try
        {
            const string sql = @"
                SELECT Id, ImagePath, Status, CreatedAt, ProcessedAt
                FROM Images 
                WHERE Status = 'Pending' 
                ORDER BY CreatedAt ASC";

            _logger.LogDebug("Fetching pending images from database");
            
            var result = await _connection.QueryAsync<ImageRecord>(sql);
            
            _logger.LogInformation("Found {Count} pending images", result.Count());
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching pending images from database");
            throw;
        }
    }

    public async Task UpdateImageStatusAsync(int imageId, string status)
    {
        try
        {
            const string sql = @"
                UPDATE Images 
                SET Status = @Status, ProcessedAt = @ProcessedAt 
                WHERE Id = @Id";

            var parameters = new
            {
                Id = imageId,
                Status = status,
                ProcessedAt = DateTime.UtcNow
            };

            _logger.LogDebug("Updating image {ImageId} status to {Status}", imageId, status);
            
            var rowsAffected = await _connection.ExecuteAsync(sql, parameters);
            
            if (rowsAffected == 0)
            {
                _logger.LogWarning("No rows affected when updating image {ImageId} status", imageId);
            }
            else
            {
                _logger.LogDebug("Successfully updated image {ImageId} status to {Status}", imageId, status);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating image {ImageId} status to {Status}", imageId, status);
            throw;
        }
    }

    public async Task LogProcessingResultAsync(int imageId, bool success, string? errorMessage = null)
    {
        try
        {
            const string sql = @"
                INSERT INTO ProcessingLog (ImageId, Success, ErrorMessage, ProcessedAt)
                VALUES (@ImageId, @Success, @ErrorMessage, @ProcessedAt)";

            var parameters = new
            {
                ImageId = imageId,
                Success = success,
                ErrorMessage = errorMessage,
                ProcessedAt = DateTime.UtcNow
            };

            _logger.LogDebug("Logging processing result for image {ImageId}: Success={Success}", imageId, success);
            
            await _connection.ExecuteAsync(sql, parameters);
            
            _logger.LogDebug("Successfully logged processing result for image {ImageId}", imageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging processing result for image {ImageId}", imageId);
            // Don't rethrow here as this is just logging
        }
    }
}
