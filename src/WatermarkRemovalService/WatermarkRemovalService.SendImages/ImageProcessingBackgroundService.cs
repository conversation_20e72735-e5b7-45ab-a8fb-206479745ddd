using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace WatermarkRemovalService.SendImages;

public class ImageProcessingBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ImageProcessingBackgroundService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromSeconds(30); // Check every 30 seconds

    public ImageProcessingBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<ImageProcessingBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Image Processing Background Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPendingImagesAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during image processing cycle");
            }

            // Wait before next processing cycle
            try
            {
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
        }

        _logger.LogInformation("Image Processing Background Service stopped");
    }

    private async Task ProcessPendingImagesAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var dataService = scope.ServiceProvider.GetRequiredService<IDataService>();
        var imageProcessingService = scope.ServiceProvider.GetRequiredService<IImageProcessingService>();

        try
        {
            var pendingImages = await dataService.GetPendingImagesAsync();

            if (!pendingImages.Any())
            {
                _logger.LogDebug("No pending images found");
                return;
            }

            _logger.LogInformation("Processing {Count} pending images", pendingImages.Count());

            foreach (var imageRecord in pendingImages)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    _logger.LogInformation("Cancellation requested, stopping image processing");
                    break;
                }

                await ProcessSingleImageAsync(dataService, imageProcessingService, imageRecord);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing pending images");
        }
    }

    private async Task ProcessSingleImageAsync(
        IDataService dataService,
        IImageProcessingService imageProcessingService,
        ImageRecord imageRecord)
    {
        try
        {
            _logger.LogInformation("Processing image {ImageId}", imageRecord.Id);

            // Update status to "Processing"
            await dataService.UpdateImageStatusAsync(imageRecord.Id, "Processing");

            // Process the image
            var success = await imageProcessingService.ProcessImageAsync(imageRecord);

            if (success)
            {
                // Update status to "Completed"
                await dataService.UpdateImageStatusAsync(imageRecord.Id, "Completed");
                await dataService.LogProcessingResultAsync(imageRecord.Id, true);
                
                _logger.LogInformation("Successfully completed processing image {ImageId}", imageRecord.Id);
            }
            else
            {
                // Update status to "Failed"
                await dataService.UpdateImageStatusAsync(imageRecord.Id, "Failed");
                await dataService.LogProcessingResultAsync(imageRecord.Id, false, "Processing failed");
                
                _logger.LogWarning("Failed to process image {ImageId}", imageRecord.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing image {ImageId}", imageRecord.Id);

            try
            {
                // Update status to "Failed" and log the error
                await dataService.UpdateImageStatusAsync(imageRecord.Id, "Failed");
                await dataService.LogProcessingResultAsync(imageRecord.Id, false, ex.Message);
            }
            catch (Exception logEx)
            {
                _logger.LogError(logEx, "Error updating failed status for image {ImageId}", imageRecord.Id);
            }
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Image Processing Background Service is stopping");
        await base.StopAsync(cancellationToken);
    }
}
