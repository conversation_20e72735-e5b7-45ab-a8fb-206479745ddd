# Watermark Removal Service - Send Images

This is a long-running console application that processes images from a database using Dapper and dependency injection.

## Features

- **Long-running background service** that continuously processes pending images
- **Dependency Injection** support with Microsoft.Extensions.DependencyInjection
- **Configuration** from appsettings.json and environment variables
- **Database access** using Dapper with SQL Server
- **Comprehensive logging** with different levels for development and production
- **Error handling** and status tracking for each image

## Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "DataEntryContext": "your-connection-string-here"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  },
  "ProcessingSettings": {
    "ProcessingIntervalSeconds": 30,
    "MaxConcurrentProcessing": 5
  }
}
```

### Environment Variables
You can override any configuration using environment variables:
- `ConnectionStrings__DataEntryContext` - Database connection string
- `ASPNETCORE_ENVIRONMENT` - Set to "Development" for development settings
- `Logging__LogLevel__Default` - Set logging level

## Database Schema

The application expects the following tables:

### Images Table
```sql
CREATE TABLE Images (
    Id INT PRIMARY KEY IDENTITY(1,1),
    ImagePath NVARCHAR(500) NOT NULL,
    Status NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ProcessedAt DATETIME2 NULL
);
```

### ProcessingLog Table (Optional)
```sql
CREATE TABLE ProcessingLog (
    Id INT PRIMARY KEY IDENTITY(1,1),
    ImageId INT NOT NULL,
    Success BIT NOT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    ProcessedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (ImageId) REFERENCES Images(Id)
);
```

## How It Works

1. **Background Service**: Runs continuously, checking for pending images every 30 seconds (configurable)
2. **Image Processing**: For each pending image:
   - Updates status to "Processing"
   - Processes the image (placeholder implementation)
   - Updates status to "Completed" or "Failed"
   - Logs the result

3. **Status Flow**: Pending → Processing → Completed/Failed

## Running the Application

### Development
```bash
# Set environment to Development
export ASPNETCORE_ENVIRONMENT=Development

# Run the application
dotnet run
```

### Production
```bash
# Build the application
dotnet build -c Release

# Run the application
dotnet run -c Release
```

## Extending the Application

### Custom Image Processing
Modify the `ImageProcessingService.ProcessImageAsync` method to implement your actual image processing logic:

```csharp
public async Task<bool> ProcessImageAsync(ImageRecord imageRecord)
{
    // Your image processing logic here
    // 1. Load image from imageRecord.ImagePath
    // 2. Apply watermark removal
    // 3. Save processed image
    // 4. Return success/failure
}
```

### Adding New Services
Register new services in `Program.cs`:

```csharp
services.AddScoped<IYourService, YourService>();
```

## Logging

The application uses structured logging with different levels:
- **Development**: Debug level and above
- **Production**: Information level and above

Logs include:
- Image processing start/completion
- Database operations
- Error details with context
- Service lifecycle events
