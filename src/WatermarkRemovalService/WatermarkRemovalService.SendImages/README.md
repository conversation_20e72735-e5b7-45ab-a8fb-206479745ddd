# Watermark Removal Service - Send Images

This is a simple one-time console application that processes images from a database using <PERSON>pper and makes HTTP API calls using Refit.

## Features

- **Simple one-time task** that processes up to 3000 pending images
- **Configuration** from appsettings.json files
- **Database access** using Dapper with SQL Server
- **HTTP API calls** using Refit with Bearer token authentication
- **Progress tracking** and error handling for each image

## Configuration

### appsettings.json

```json
{
  "ConnectionStrings": {
    "DataEntryContext": "your-connection-string-here"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  },
  "ApiSettings": {
    "BaseUrl": "https://api.example.com",
    "BearerToken": "your-bearer-token-here"
  }
}
```

### Environment Variables

You can set `ASPNETCORE_ENVIRONMENT=Development` to use development configuration.

## Database Schema

The application expects the following tables:

### Images Table
```sql
CREATE TABLE Images (
    Id INT PRIMARY KEY IDENTITY(1,1),
    ImagePath NVARCHAR(500) NOT NULL,
    Status NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ProcessedAt DATETIME2 NULL
);
```

### ProcessingLog Table (Optional)
```sql
CREATE TABLE ProcessingLog (
    Id INT PRIMARY KEY IDENTITY(1,1),
    ImageId INT NOT NULL,
    Success BIT NOT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    ProcessedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (ImageId) REFERENCES Images(Id)
);
```

## How It Works

1. **One-time execution**: Reads all pending images from database (up to 3000 records)
2. **Image Processing**: For each pending image:
   - Updates status to "Processing"
   - Makes HTTP API call using Refit with Bearer token authentication
   - Updates status to "Completed" or "Failed" based on API response
   - Shows progress every 100 processed images

3. **Status Flow**: Pending → Processing → Completed/Failed

## Running the Application

### Development

```bash
# Set environment to Development
export ASPNETCORE_ENVIRONMENT=Development

# Run the application
dotnet run
```

### Production

```bash
# Build the application
dotnet build -c Release

# Run the application
dotnet run -c Release
```

## API Integration

The application uses Refit to make HTTP calls to a web API with Bearer token authentication. The API endpoint expects:

**Request:**
```json
{
  "imageId": 123,
  "imagePath": "/path/to/image.jpg",
  "processingType": "WatermarkRemoval"
}
```

**Response:**
```json
{
  "success": true,
  "errorMessage": null,
  "processedImageUrl": "https://api.example.com/processed/image.jpg"
}
```

## Console Output

The application provides real-time feedback:

```
Starting image processing task...
Found 150 pending images to process.
Processing image 1: /images/photo1.jpg
✓ Successfully processed image 1
Processing image 2: /images/photo2.jpg
✗ Failed to process image 2: Invalid image format
Progress: 100/150 processed
...
Processing completed:
Total processed: 150
Successful: 145
Failed: 5
```
