namespace WatermarkRemovalService.SendImages;

public interface IDataService
{
    Task<IEnumerable<ImageRecord>> GetPendingImagesAsync();
    Task UpdateImageStatusAsync(int imageId, string status);
    Task LogProcessingResultAsync(int imageId, bool success, string? errorMessage = null);
}

public class ImageRecord
{
    public int Id { get; set; }
    public string ImagePath { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
}
