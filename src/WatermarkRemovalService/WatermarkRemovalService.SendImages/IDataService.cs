using Refit;

namespace WatermarkRemovalService.SendImages;

// Database model
public class ImageRecord
{
    public int Id { get; set; }
    public string ImagePath { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
}

// API models
public class ProcessImageRequest
{
    public int ImageId { get; set; }
    public string ImagePath { get; set; } = string.Empty;
    public string ProcessingType { get; set; } = string.Empty;
}

public class ProcessImageResponse
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ProcessedImageUrl { get; set; }
}

// Refit API interface
public interface IImageProcessingApi
{
    [Post("/api/images/process")]
    Task<ProcessImageResponse> ProcessImageAsync([Body] ProcessImageRequest request);
}
