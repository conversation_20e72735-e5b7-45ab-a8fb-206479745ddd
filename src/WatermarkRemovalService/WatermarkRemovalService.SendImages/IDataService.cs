using Refit;
using System.Text.Json.Serialization;

namespace WatermarkRemovalService.SendImages;

// Database models
public record ImageRecord(
    int ListingPictureId,
    int ListingId,
    string ListingNumber,
    string ImageUrl);

// API models
public record ProcessImageRequest(
    [property: JsonPropertyName("image_url")] string ImageUrl);
public record ProcessImageResponse(
    int Code,
    string Message,
    ProcessImageDataResponse Data);
public record ProcessImageDataResponse(
    [property: JsonPropertyName("task_id")] string TaskId);

// Refit API interface
public interface IImageProcessingApi
{
    [Post("/submit_watermark_removal")]
    Task<ProcessImageResponse> ProcessImageAsync([Body] ProcessImageRequest request);
}
