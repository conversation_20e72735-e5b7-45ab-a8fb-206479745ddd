﻿using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Refit;
using System.Net.Http.Headers;
using WatermarkRemovalService.RetrieveImages;


// Build configuration
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false)
    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
    .Build();

var connectionString = configuration.GetConnectionString("DataEntryContext")
    ?? throw new InvalidOperationException("Connection string 'DataEntryContext' not found.");
var bearerToken = configuration["ApiSettings:BearerToken"]
    ?? throw new InvalidOperationException("Bearer token not found in configuration.");
var apiBaseUrl = configuration["ApiSettings:BaseUrl"]
    ?? throw new InvalidOperationException("API base URL not found in configuration.");
var backupFolder = configuration["Folders:Backup"]
    ?? throw new InvalidOperationException("Backup folder not found in configuration.");
var processedFolder = configuration["Folders:Processed"]
    ?? throw new InvalidOperationException("Processed folder not found in configuration.");

Console.WriteLine("Starting image retreiving task...");

try
{
    // Setup HTTP client with Bearer token
    var httpClient = new HttpClient();
    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", bearerToken);
    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    httpClient.BaseAddress = new Uri(apiBaseUrl);
    // Create Refit client
    var apiClient = RestService.For<IImageProcessingApi>(httpClient, new RefitSettings
    {
        ContentSerializer = new SystemTextJsonContentSerializer()
    });

    // Process images
    await ProcessImagesAsync(connectionString, backupFolder, processedFolder, apiClient);

    Console.WriteLine("Image retreiving task completed successfully.");
}
catch (Exception ex)
{
    Console.Error.WriteLine($"Error: {ex.Message}");
    Console.Error.WriteLine($"Stack trace: {ex.StackTrace}");
    Environment.Exit(1);
}

static async Task ProcessImagesAsync(
    string connectionString,
    string backupFolder,
    string processedFolder,
    IImageProcessingApi apiClient)
{
    using var connection = new SqlConnection(connectionString);
    await connection.OpenAsync();

    // Get all pending logs
    var logs = await connection.QueryAsync<ListingMediaLogRecord>(@"
        select Id,
               ListingPictureId,
               TaskId  
          from ListingMediaProcessingLog
         where Status = 'Submitted'
    ");

    if (logs == null || !logs.Any())
    {
        Console.WriteLine("No pending images found to retrieve.");
        return;
    }

    const int failedThreshold = 5;

    var total = logs.Count();

    Console.WriteLine($"Processing {total} pending images to retrieve.");

    int processed = 0;
    int successful = 0;
    int failed = 0;

    foreach (var log in logs)
    {
        try
        {
            Console.WriteLine($"Retrieving image by task id: {log.TaskId}");

            // Make API call
            var request = new GetTaskRequest(log.TaskId);
            var response = await apiClient.GetTaskResultAsync(request);

            if (string.Equals(response.Message, "success", StringComparison.OrdinalIgnoreCase))
            {
                if (string.Equals(response.Data.Status, "success", StringComparison.OrdinalIgnoreCase))
                {
                    // update log status to Processed
                    await connection.ExecuteAsync(@"    
                            update ListingMediaProcessingLog
                               set Status = 'Processed',
                          ImageUrl = @ImageUrl,
                         UpdatedOn = GETDATE()
                             where Id = @Id
                        ",
                        new
                        {
                            log.Id,
                            ImageUrl = response.Data.Images[0].Url
                        });
                }
                else if (string.Equals(response.Data.Status, "failed", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(response.Data.Status, "timeout", StringComparison.OrdinalIgnoreCase))
                {

                }
            }
            else
            {

            }
            //if (response.Code == 0)
            //{
            //    // update log status to Submitted with TaskId
            //    await connection.ExecuteAsync(@"    
            //        update ListingMediaProcessingLog
            //        set Status = 'Submitted',
            //            StatusCode = @Code,
            //            StatusMessage = @Message,
            //            TaskId = @TaskId,
            //            UpdatedOn = GETDATE()
            //        where Id = @Id
            //    ",
            //    new
            //    {
            //        Id = recordId,
            //        response.Code,
            //        response.Message,
            //        response.Data.TaskId
            //    });

            //    successful++;
            //    Console.WriteLine($"✓ Successfully processed image id: {log.ListingId}");
            //}
            //else
            //{
            //    // Update status to Failed
            //    await connection.ExecuteAsync(@"    
            //        update ListingMediaProcessingLog
            //        set Status = 'Failed',
            //            StatusCode = @Code,
            //            StatusMessage = @Message
            //            UpdatedOn = GETDATE()
            //        where Id = @Id
            //    ",
            //    new
            //    {
            //        Id = recordId,
            //        response.Code,
            //        response.Message
            //    });

            //    failed++;
            //    Console.WriteLine($"✗ Failed to process image id: {log.ListingPictureId}: {response.Message}");
            //}
        }
        catch (Exception ex)
        {
            // Update status to Failed
            await connection.ExecuteAsync(@"    
                    update ListingMediaProcessingLog
                    set Status = 'Failed',
                        StatusCode = -1,
                        StatusMessage = @Message
                        UpdatedOn = GETDATE()
                    where Id = @Id
                ",
            new
            {
                log.Id,
                ex.Message
            });

            failed++;
            Console.Error.WriteLine($"✗ Error retrieving image id: {log.ListingPictureId}: {ex.Message}");
        }

        processed++;

        if (failed >= failedThreshold)
        {
            Console.Error.WriteLine($"Stopped retrieving due to too many failed attempts.");
            break;
        }
        if (processed % 50 == 0)
        {
            Console.WriteLine($"Progress: {processed}/{total} retrieved.");
        }
    }

    Console.WriteLine($"\nRetrieval completed:");
    Console.WriteLine($"Total retrieved: {processed}");
    Console.WriteLine($"Successful: {successful}");
    Console.WriteLine($"Failed: {failed}");
}
