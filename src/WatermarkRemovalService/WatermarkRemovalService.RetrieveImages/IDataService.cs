using Refit;
using System.Text.Json.Serialization;

namespace WatermarkRemovalService.RetrieveImages;

// Database models
public record ListingMediaLogRecord(int Id, int ListingPictureId, string TaskId);

// API models
public record GetTaskRequest([property: JsonPropertyName("task_id")] string TaskId);
public record GetTaskResponse(
    string Message,
    GetTaskDataResponse Data);
public record GetTaskDataResponse(
    string Status,
    GetTaskDataImageResponse[] Images);
public record GetTaskDataImageResponse(string Url);

// Refit API interface
public interface IImageProcessingApi
{
    [Post("/get_task_result")]
    Task<GetTaskResponse> GetTaskResultAsync([Body] GetTaskRequest request);
}
